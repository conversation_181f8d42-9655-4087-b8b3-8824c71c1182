import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { X, Key, Sliders, Save, Server, Wifi, WifiOff } from './Icons'
import { openRouterService } from '../services/openrouter'
import { localModelService } from '../services/localModelService'

interface SettingsProps {
  isOpen: boolean
  onClose: () => void
}

const Settings: React.FC<SettingsProps> = ({ isOpen, onClose }) => {
  const { settings, updateSettings, setModels } = useAppStore()
  const [localSettings, setLocalSettings] = useState(settings)
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<string | null>(null)
  const [localModelStatus, setLocalModelStatus] = useState<any>(null)
  const [activeTab, setActiveTab] = useState<'api' | 'local'>('api')

  useEffect(() => {
    setLocalSettings(settings)
    // Initialize default local model server settings if not present
    if (!settings.localModelServers) {
      setLocalSettings(prev => ({
        ...prev,
        localModelServers: {
          ollama: {
            enabled: true,
            baseUrl: 'localhost',
            port: 11434
          },
          lmstudio: {
            enabled: true,
            baseUrl: 'localhost',
            port: 1234
          }
        }
      }))
    }
  }, [settings])

  const handleSave = async () => {
    try {
      setIsLoading(true)

      // Update local model service with new settings
      localModelService.setSettings(localSettings)

      // Update settings in store and save to electron
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('app-settings', localSettings)
      }

      // If API key changed, test it and load models
      if (localSettings.openRouterApiKey && localSettings.openRouterApiKey !== settings.openRouterApiKey) {
        openRouterService.setApiKey(localSettings.openRouterApiKey)
        const models = await openRouterService.getModels()
        setModels(models)

        if (models.length > 0) {
          setTestResult('✅ API key is valid! Loaded ' + models.length + ' models.')
        } else {
          setTestResult('⚠️ API key seems valid but no models were loaded.')
        }
      }

      setTimeout(() => {
        onClose()
      }, 1000)

    } catch (error) {
      console.error('Failed to save settings:', error)
      setTestResult('❌ Failed to save settings: ' + (error as Error).message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestLocalModels = async () => {
    try {
      setIsLoading(true)
      setTestResult('Testing local model connections...')

      // Update local model service with current settings
      localModelService.setSettings(localSettings)

      // Test connections
      const status = await localModelService.getProviderStatus()
      setLocalModelStatus(status)

      const results = []
      if (status.ollama.isConnected) {
        results.push(`✅ Ollama: ${status.ollama.models.length} models`)
      } else {
        results.push(`❌ Ollama: ${status.ollama.error || 'Not connected'}`)
      }

      if (status.lmstudio.isConnected) {
        results.push(`✅ LM Studio: ${status.lmstudio.models.length} models`)
      } else {
        results.push(`❌ LM Studio: ${status.lmstudio.error || 'Not connected'}`)
      }

      setTestResult(results.join('\n'))
    } catch (error) {
      setTestResult('❌ Failed to test local models: ' + (error as Error).message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestApiKey = async () => {
    if (!localSettings.openRouterApiKey) {
      setTestResult('❌ Please enter an API key first')
      return
    }

    try {
      setIsLoading(true)
      setTestResult('Testing API key...')

      openRouterService.setApiKey(localSettings.openRouterApiKey)

      // Use the new validation method
      const validation = await openRouterService.validateApiKey()

      if (validation.valid) {
        // If validation passes, load models
        const models = await openRouterService.getModels()

        if (models.length > 0) {
          setTestResult('✅ API key is valid! Found ' + models.length + ' models.')
          setModels(models)
        } else {
          setTestResult('⚠️ API key seems valid but no models were loaded.')
        }
      } else {
        setTestResult('❌ API key validation failed: ' + validation.error)
      }
    } catch (error) {
      setTestResult('❌ API key test failed: ' + (error as Error).message)
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-neutral-900 rounded-lg border border-neutral-800 w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-800">
          <h2 className="text-lg font-semibold">API & Server Settings</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-neutral-800 rounded"
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-neutral-800">
          <button
            onClick={() => setActiveTab('api')}
            className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === 'api'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-neutral-800/50'
                : 'text-neutral-400 hover:text-neutral-300'
            }`}
          >
            <Key className="h-4 w-4 inline mr-2" />
            API Keys
          </button>
          <button
            onClick={() => setActiveTab('local')}
            className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === 'local'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-neutral-800/50'
                : 'text-neutral-400 hover:text-neutral-300'
            }`}
          >
            <Server className="h-4 w-4 inline mr-2" />
            Local Servers
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'api' && (
            <div className="space-y-6">
              {/* ChatLo API Key */}
              <div>
                <label className="flex items-center gap-2 text-sm font-medium mb-2">
                  <Key className="h-4 w-4" />
                  ChatLo API Key
                </label>
                <input
                  type="password"
                  value={localSettings.chatloApiKey || ''}
                  onChange={(e) => setLocalSettings({ ...localSettings, chatloApiKey: e.target.value })}
                  placeholder="chatlo-..."
                  className="input-field w-full"
                />
                <p className="text-xs text-neutral-500 mt-1">
                  For ChatLo cloud services and premium features
                </p>
              </div>

              {/* OpenRouter API Key */}
              <div>
                <label className="flex items-center gap-2 text-sm font-medium mb-2">
                  <Key className="h-4 w-4" />
                  OpenRouter API Key
                </label>
                <input
                  type="password"
                  value={localSettings.openRouterApiKey || ''}
                  onChange={(e) => setLocalSettings({ ...localSettings, openRouterApiKey: e.target.value })}
                  placeholder="sk-or-..."
                  className="input-field w-full"
                />
                <div className="flex items-center gap-2 mt-2">
                  <button
                    onClick={handleTestApiKey}
                    disabled={isLoading}
                    className="btn-secondary text-xs"
                  >
                    Test API Key
                  </button>
                  <a
                    href="https://openrouter.ai/keys"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-indigo-400 hover:text-indigo-300"
                  >
                    Get API Key
                  </a>
                </div>
              </div>

              {/* Info about chat settings */}
              <div className="bg-neutral-800/50 border border-neutral-700 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Sliders className="h-4 w-4 text-indigo-400" />
                  <h3 className="font-medium">Chat Configuration</h3>
                </div>
                <p className="text-sm text-neutral-400 mb-3">
                  Model selection, system prompts, and advanced parameters can be configured using the settings gear icon next to the send button in any chat.
                </p>
                <div className="text-xs text-neutral-500">
                  💡 This allows you to adjust settings contextually while chatting
                </div>
              </div>
            </div>
          )}

          {activeTab === 'local' && (
            <div className="space-y-6">
              {/* Ollama Configuration */}
              <div className="bg-neutral-800/50 border border-neutral-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Server className="h-5 w-5 text-indigo-400" />
                    <h3 className="font-medium">Ollama</h3>
                  </div>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={localSettings.localModelServers?.ollama?.enabled !== false}
                      onChange={(e) => setLocalSettings({
                        ...localSettings,
                        localModelServers: {
                          ...localSettings.localModelServers,
                          ollama: {
                            ...localSettings.localModelServers?.ollama,
                            enabled: e.target.checked,
                            baseUrl: localSettings.localModelServers?.ollama?.baseUrl || 'localhost',
                            port: localSettings.localModelServers?.ollama?.port || 11434
                          }
                        }
                      })}
                      className="rounded"
                    />
                    <span className="text-sm">Enabled</span>
                  </label>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-1 block">Host</label>
                    <input
                      type="text"
                      value={localSettings.localModelServers?.ollama?.baseUrl || 'localhost'}
                      onChange={(e) => setLocalSettings({
                        ...localSettings,
                        localModelServers: {
                          ...localSettings.localModelServers,
                          ollama: {
                            ...localSettings.localModelServers?.ollama,
                            baseUrl: e.target.value,
                            enabled: localSettings.localModelServers?.ollama?.enabled !== false,
                            port: localSettings.localModelServers?.ollama?.port || 11434
                          }
                        }
                      })}
                      className="input-field w-full"
                      placeholder="localhost"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-1 block">Port</label>
                    <input
                      type="number"
                      value={localSettings.localModelServers?.ollama?.port || 11434}
                      onChange={(e) => setLocalSettings({
                        ...localSettings,
                        localModelServers: {
                          ...localSettings.localModelServers,
                          ollama: {
                            ...localSettings.localModelServers?.ollama,
                            port: parseInt(e.target.value) || 11434,
                            enabled: localSettings.localModelServers?.ollama?.enabled !== false,
                            baseUrl: localSettings.localModelServers?.ollama?.baseUrl || 'localhost'
                          }
                        }
                      })}
                      className="input-field w-full"
                      placeholder="11434"
                    />
                  </div>
                </div>
              </div>

              {/* LM Studio Configuration */}
              <div className="bg-neutral-800/50 border border-neutral-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Server className="h-5 w-5 text-indigo-400" />
                    <h3 className="font-medium">LM Studio</h3>
                  </div>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={localSettings.localModelServers?.lmstudio?.enabled !== false}
                      onChange={(e) => setLocalSettings({
                        ...localSettings,
                        localModelServers: {
                          ...localSettings.localModelServers,
                          lmstudio: {
                            ...localSettings.localModelServers?.lmstudio,
                            enabled: e.target.checked,
                            baseUrl: localSettings.localModelServers?.lmstudio?.baseUrl || 'localhost',
                            port: localSettings.localModelServers?.lmstudio?.port || 1234
                          }
                        }
                      })}
                      className="rounded"
                    />
                    <span className="text-sm">Enabled</span>
                  </label>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-1 block">Host</label>
                    <input
                      type="text"
                      value={localSettings.localModelServers?.lmstudio?.baseUrl || 'localhost'}
                      onChange={(e) => setLocalSettings({
                        ...localSettings,
                        localModelServers: {
                          ...localSettings.localModelServers,
                          lmstudio: {
                            ...localSettings.localModelServers?.lmstudio,
                            baseUrl: e.target.value,
                            enabled: localSettings.localModelServers?.lmstudio?.enabled !== false,
                            port: localSettings.localModelServers?.lmstudio?.port || 1234
                          }
                        }
                      })}
                      className="input-field w-full"
                      placeholder="localhost"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-1 block">Port</label>
                    <input
                      type="number"
                      value={localSettings.localModelServers?.lmstudio?.port || 1234}
                      onChange={(e) => setLocalSettings({
                        ...localSettings,
                        localModelServers: {
                          ...localSettings.localModelServers,
                          lmstudio: {
                            ...localSettings.localModelServers?.lmstudio,
                            port: parseInt(e.target.value) || 1234,
                            enabled: localSettings.localModelServers?.lmstudio?.enabled !== false,
                            baseUrl: localSettings.localModelServers?.lmstudio?.baseUrl || 'localhost'
                          }
                        }
                      })}
                      className="input-field w-full"
                      placeholder="1234"
                    />
                  </div>
                </div>
              </div>

              {/* Test Local Models */}
              <div className="flex items-center gap-2">
                <button
                  onClick={handleTestLocalModels}
                  disabled={isLoading}
                  className="btn-secondary flex items-center gap-2"
                >
                  {localModelStatus?.ollama?.isConnected || localModelStatus?.lmstudio?.isConnected ? (
                    <Wifi className="h-4 w-4" />
                  ) : (
                    <WifiOff className="h-4 w-4" />
                  )}
                  Test Connections
                </button>
              </div>
            </div>
          )}

          {/* Test Results */}
          {testResult && (
            <div className="bg-neutral-800/50 border border-neutral-700 rounded-lg p-4">
              <pre className="text-xs text-neutral-300 whitespace-pre-wrap">{testResult}</pre>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-neutral-800">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="btn-primary flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {isLoading ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default Settings
