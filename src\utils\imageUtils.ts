// Image processing utilities

export interface ImageValidationResult {
  isValid: boolean
  error?: string
  warnings?: string[]
}

export interface ImageOptimizationOptions {
  maxWidth?: number
  maxHeight?: number
  maxFileSize?: number // in bytes
  quality?: number // 0-1 for JPEG
  format?: 'jpeg' | 'png' | 'webp'
}

// Supported image formats
export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
  'image/svg+xml',
  'image/tiff',
  'image/avif'
]

// Default size limits
export const DEFAULT_LIMITS = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxWidth: 4096,
  maxHeight: 4096,
  minWidth: 32,
  minHeight: 32
}

/**
 * Validate image file before processing
 */
export function validateImageFile(file: File): ImageValidationResult {
  const warnings: string[] = []
  
  // Check file type
  if (!SUPPORTED_IMAGE_FORMATS.includes(file.type)) {
    return {
      isValid: false,
      error: `Unsupported image format: ${file.type}. Supported formats: JPEG, PNG, GIF, WebP, BMP, SVG`
    }
  }

  // Check file size
  if (file.size > DEFAULT_LIMITS.maxFileSize) {
    return {
      isValid: false,
      error: `File too large: ${formatFileSize(file.size)}. Maximum size: ${formatFileSize(DEFAULT_LIMITS.maxFileSize)}`
    }
  }

  // Warnings for large files
  if (file.size > 5 * 1024 * 1024) { // 5MB
    warnings.push(`Large file size: ${formatFileSize(file.size)}. Consider optimizing for better performance.`)
  }

  return {
    isValid: true,
    warnings: warnings.length > 0 ? warnings : undefined
  }
}

/**
 * Get image dimensions from file
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    const url = URL.createObjectURL(file)
    
    img.onload = () => {
      URL.revokeObjectURL(url)
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
    }
    
    img.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('Failed to load image'))
    }
    
    img.src = url
  })
}

/**
 * Validate image dimensions
 */
export async function validateImageDimensions(file: File): Promise<ImageValidationResult> {
  try {
    const { width, height } = await getImageDimensions(file)
    const warnings: string[] = []

    // Check minimum dimensions
    if (width < DEFAULT_LIMITS.minWidth || height < DEFAULT_LIMITS.minHeight) {
      return {
        isValid: false,
        error: `Image too small: ${width}×${height}. Minimum size: ${DEFAULT_LIMITS.minWidth}×${DEFAULT_LIMITS.minHeight}`
      }
    }

    // Check maximum dimensions
    if (width > DEFAULT_LIMITS.maxWidth || height > DEFAULT_LIMITS.maxHeight) {
      return {
        isValid: false,
        error: `Image too large: ${width}×${height}. Maximum size: ${DEFAULT_LIMITS.maxWidth}×${DEFAULT_LIMITS.maxHeight}`
      }
    }

    // Warnings for very large images
    if (width > 2048 || height > 2048) {
      warnings.push(`Large image dimensions: ${width}×${height}. Consider resizing for better performance.`)
    }

    return {
      isValid: true,
      warnings: warnings.length > 0 ? warnings : undefined
    }
  } catch (error) {
    return {
      isValid: false,
      error: 'Failed to read image dimensions'
    }
  }
}

/**
 * Resize image using canvas
 */
export function resizeImage(
  file: File, 
  options: ImageOptimizationOptions
): Promise<{ file: File; originalSize: number; newSize: number }> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    const url = URL.createObjectURL(file)

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    img.onload = () => {
      URL.revokeObjectURL(url)

      const { width: originalWidth, height: originalHeight } = img
      const maxWidth = options.maxWidth || DEFAULT_LIMITS.maxWidth
      const maxHeight = options.maxHeight || DEFAULT_LIMITS.maxHeight

      // Calculate new dimensions
      let { width, height } = calculateResizeDimensions(
        originalWidth, 
        originalHeight, 
        maxWidth, 
        maxHeight
      )

      canvas.width = width
      canvas.height = height

      // Draw resized image
      ctx.drawImage(img, 0, 0, width, height)

      // Convert to blob
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to create resized image'))
            return
          }

          const resizedFile = new File([blob], file.name, {
            type: options.format ? `image/${options.format}` : file.type,
            lastModified: Date.now()
          })

          resolve({
            file: resizedFile,
            originalSize: file.size,
            newSize: resizedFile.size
          })
        },
        options.format ? `image/${options.format}` : file.type,
        options.quality || 0.9
      )
    }

    img.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('Failed to load image for resizing'))
    }

    img.src = url
  })
}

/**
 * Calculate resize dimensions maintaining aspect ratio
 */
function calculateResizeDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  const aspectRatio = originalWidth / originalHeight

  let width = originalWidth
  let height = originalHeight

  // Scale down if too wide
  if (width > maxWidth) {
    width = maxWidth
    height = width / aspectRatio
  }

  // Scale down if too tall
  if (height > maxHeight) {
    height = maxHeight
    width = height * aspectRatio
  }

  return {
    width: Math.round(width),
    height: Math.round(height)
  }
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

/**
 * Check if image needs optimization
 */
export function shouldOptimizeImage(file: File): boolean {
  const maxSizeForOptimization = 2 * 1024 * 1024 // 2MB
  return file.size > maxSizeForOptimization
}

/**
 * Get image type from filename
 */
export function getImageTypeFromFilename(filename: string): string {
  const ext = filename.toLowerCase().split('.').pop()
  switch (ext) {
    case 'jpg':
    case 'jpeg':
      return 'photograph'
    case 'png':
      return 'graphic'
    case 'gif':
      return 'animation'
    case 'svg':
      return 'scalable'
    case 'webp':
      return 'optimized'
    case 'avif':
      return 'next-gen'
    case 'tiff':
      return 'professional'
    default:
      return 'image'
  }
}

/**
 * Convert image to WebP format for better compression
 */
export function convertToWebP(
  file: File,
  quality: number = 0.8
): Promise<{ file: File; originalSize: number; newSize: number }> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    const url = URL.createObjectURL(file)

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    img.onload = () => {
      URL.revokeObjectURL(url)

      canvas.width = img.width
      canvas.height = img.height

      // Draw image
      ctx.drawImage(img, 0, 0)

      // Convert to WebP
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to convert to WebP'))
            return
          }

          const webpFilename = file.name.replace(/\.[^/.]+$/, '.webp')
          const webpFile = new File([blob], webpFilename, {
            type: 'image/webp',
            lastModified: Date.now()
          })

          resolve({
            file: webpFile,
            originalSize: file.size,
            newSize: webpFile.size
          })
        },
        'image/webp',
        quality
      )
    }

    img.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('Failed to load image for WebP conversion'))
    }

    img.src = url
  })
}

/**
 * Smart format recommendation based on image content
 */
export function recommendFormat(file: File): 'webp' | 'png' | 'jpeg' {
  const filename = file.name.toLowerCase()

  // Keep PNG for graphics with transparency
  if (filename.includes('logo') || filename.includes('icon') || filename.includes('graphic')) {
    return 'png'
  }

  // Use JPEG for photos
  if (filename.includes('photo') || filename.includes('img') || file.type === 'image/jpeg') {
    return 'jpeg'
  }

  // Default to WebP for best compression
  return 'webp'
}
