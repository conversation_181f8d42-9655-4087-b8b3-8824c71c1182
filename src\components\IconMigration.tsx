import React from 'react'
import {
  HomeIcon,
  ChatIcon,
  HistoryIcon,
  FilesIcon,
  UserIcon,
  SettingsIcon,
  WifiIcon,
  InfoIcon,
  CircleIcon,
  CheckIcon,
  PaperclipIcon,
  MoreIcon,
  PlusIcon,
  SendIcon,
  WrenchIcon,
  SearchIcon,
  TrashIcon,
  EditIcon,
  CheckCircleIcon,
  AlertIcon,
  PinIcon,
  MoreHorizontalIcon,
  FileIcon,
  ImageIcon,
  FileTextIcon,
  StarIcon,
  BrainIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  EyeIcon,
  DownloadIcon,
  CopyIcon,
  XIcon,
  KeyIcon,
  ArrowLeftIcon,
  MenuIcon,
  BotIcon,
  Loader2Icon
} from './IconSystem'

// Legacy interface for backward compatibility
interface IconProps {
  className?: string
  size?: number
}

// Migration mapping from old Icons.tsx to new IconSystem
// This provides backward compatibility while we migrate components

export const Menu = MenuIcon
export const Bot = BotIcon
export const User = UserIcon
export const Send = SendIcon
export const Plus = PlusIcon
export const Settings = SettingsIcon
export const Wrench = WrenchIcon
export const Loader2 = Loader2Icon
export const X = XIcon
export const Copy = CopyIcon
export const Search = SearchIcon
export const Trash2 = TrashIcon
export const Edit3 = EditIcon
export const CheckCircle = CheckCircleIcon
export const AlertCircle = AlertIcon
export const Pin = PinIcon
export const MoreHorizontal = MoreHorizontalIcon
export const History = HistoryIcon
export const ArrowLeft = ArrowLeftIcon
export const FileText = FileTextIcon
export const Key = KeyIcon
export const Star = StarIcon
export const Brain = BrainIcon
export const ChevronDown = ChevronDownIcon
export const ChevronRight = ChevronRightIcon
export const Eye = EyeIcon
export const Download = DownloadIcon
export const File = FileIcon
export const Image = ImageIcon

// Custom icons that don't have FontAwesome equivalents
// These are kept as custom SVG components

const CustomIcon: React.FC<IconProps & { children: React.ReactNode }> = ({ 
  className = "h-4 w-4", 
  size, 
  children 
}) => {
  const sizeClass = size ? `h-${size} w-${size}` : className
  return (
    <svg
      className={sizeClass}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      {children}
    </svg>
  )
}

export const MessageSquare: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
  </CustomIcon>
)

export const Sliders: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <line x1="4" x2="4" y1="21" y2="14" />
    <line x1="4" x2="4" y1="10" y2="3" />
    <line x1="12" x2="12" y1="21" y2="12" />
    <line x1="12" x2="12" y1="8" y2="3" />
    <line x1="20" x2="20" y1="21" y2="16" />
    <line x1="20" x2="20" y1="12" y2="3" />
    <line x1="1" x2="7" y1="14" y2="14" />
    <line x1="9" x2="15" y1="8" y2="8" />
    <line x1="17" x2="23" y1="16" y2="16" />
  </CustomIcon>
)

export const Check: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <path d="m9 12 2 2 4-4" />
  </CustomIcon>
)

export const Code: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <polyline points="16,18 22,12 16,6" />
    <polyline points="8,6 2,12 8,18" />
  </CustomIcon>
)

export const RefreshCw: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
    <path d="M21 3v5h-5" />
    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
    <path d="M3 21v-5h5" />
  </CustomIcon>
)

export const RotateCcw: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
    <path d="M3 3v5h5" />
  </CustomIcon>
)

export const Crop: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <path d="M6 2v14a2 2 0 0 0 2 2h14" />
    <path d="M18 6H8a2 2 0 0 0-2 2v10" />
  </CustomIcon>
)

export const FileSpreadsheet: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
    <path d="M14 2v4a2 2 0 0 0 2 2h4" />
    <path d="M8 13h2" />
    <path d="M14 13h2" />
    <path d="M8 17h2" />
    <path d="M14 17h2" />
  </CustomIcon>
)

export const Presentation: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <path d="M2 3h20v14H2z" />
    <path d="m7 21 5-5 5 5" />
  </CustomIcon>
)

export const Zap: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <path d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z" />
  </CustomIcon>
)

export const Gift: React.FC<IconProps> = (props) => (
  <CustomIcon {...props}>
    <rect x="3" y="8" width="18" height="4" rx="1" />
    <path d="M12 8v13" />
    <path d="M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7" />
    <path d="M7.5 8a2.5 2.5 0 0 1 0-5A4.9 4.9 0 0 1 12 8a4.9 4.9 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5" />
  </CustomIcon>
)

// Re-export everything for easy migration
export * from './IconSystem'
