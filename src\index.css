@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@layer base {

  body {
    @apply bg-gray-900 text-white font-sans antialiased;
    font-family: 'Inter', system-ui, sans-serif;
  }

  html, body, #root {
    @apply h-full;
  }
}

@layer components {
  /* Hide scrollbars for artifact tabs */
  .artifact-tabs-scroll {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .artifact-tabs-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* ChatLo Design System v1 - Optimized Components */

  /* Buttons - Consolidated */
  .u1-button-primary {
    @apply bg-primary hover:bg-primary/80 text-gray-900 font-medium py-3 px-6 rounded-lg transition-colors;
  }

  .u1-button-secondary {
    @apply bg-secondary hover:bg-secondary/80 text-white font-medium py-3 px-6 rounded-lg transition-colors;
  }

  .u1-button-outline {
    @apply border border-primary text-primary hover:bg-primary hover:text-gray-900 font-medium py-3 px-6 rounded-lg transition-colors;
  }

  .u1-button-icon {
    @apply w-10 h-10 bg-primary hover:bg-primary/80 text-gray-900 rounded-lg transition-colors flex items-center justify-center;
  }

  .u1-button-ghost {
    @apply hover:bg-gray-700 text-supplement1 p-2 rounded-lg transition-colors;
  }

  /* Input Fields - Consolidated */
  .u1-input-field, .u1-input-search {
    @apply bg-gray-900 border border-gray-700 rounded-lg px-4 py-3 text-sm placeholder-gray-500 focus:ring-2 focus:ring-primary focus:border-transparent outline-none;
  }

  .u1-textarea {
    @apply bg-gray-900 border border-gray-700 rounded-lg px-4 py-3 text-sm placeholder-gray-500 focus:ring-2 focus:ring-primary focus:border-transparent outline-none resize-none;
  }

  .u1-chat-input {
    @apply flex gap-3 items-center;
  }

  /* Cards & Containers */
  .u1-card {
    @apply bg-gray-800 rounded-lg p-6;
  }

  .u1-chat-bubble-user {
    @apply bg-primary rounded-2xl rounded-tr-md p-4 max-w-lg text-gray-900;
  }

  .u1-chat-bubble-assistant {
    @apply bg-gray-800 rounded-2xl rounded-tl-md p-4 max-w-lg text-supplement1;
  }

  .u1-sidebar-item {
    @apply flex gap-3 items-center w-full px-6 py-3 text-sm font-medium hover:bg-gray-700 focus:bg-gray-700 transition-colors rounded-lg cursor-pointer;
  }

  .u1-status-card {
    @apply bg-gray-700/50 rounded-lg p-3;
  }

  .u1-artifact-card {
    @apply bg-gray-700/50 rounded-lg p-4;
  }

  /* Navigation - Optimized */
  .u1-nav-iconbar {
    @apply w-12 bg-gray-900 flex flex-col items-center py-2;
  }

  .u1-nav-icon {
    @apply w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1;
  }

  .u1-nav-tabs {
    @apply flex gap-2;
  }

  .u1-tab-active {
    @apply bg-primary/20 text-primary border border-primary/30 px-3 py-2 rounded-t-lg text-sm font-medium;
  }

  .u1-tab-inactive {
    @apply text-gray-400 hover:text-supplement1 px-3 py-2 rounded-t-lg text-sm font-medium transition-colors;
  }

  /* Badges & Labels */
  .u1-badge-primary {
    @apply bg-primary/20 text-primary border border-primary/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  .u1-badge-secondary {
    @apply bg-secondary/20 text-secondary border border-secondary/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  .u1-badge-success {
    @apply bg-green-500/20 text-green-400 border border-green-500/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  .u1-badge-warning {
    @apply bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  .u1-badge-error {
    @apply bg-red-500/20 text-red-400 border border-red-500/30 px-2 py-1 rounded-full text-xs font-medium;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #525252;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #737373;
}

/* Selection */
::selection {
  background: rgba(99, 102, 241, 0.6);
}
