import React, { useState, useEffect } from 'react';
import { MessageSquare, Edit3, Trash2, Pin } from './IconMigration';
import { Conversation } from '../types';
import { PaperclipIcon } from './IconSystem';

interface ConversationItemProps {
  conversation: Conversation;
  currentConversationId: string | null;
  handleSelectConversation: (id: string) => void;
  handleEditStart: (id: string, title: string, e: React.MouseEvent) => void;
  handleDeleteConversation: (id: string, e: React.MouseEvent) => void;
  editingId: string | null;
  editTitle: string;
  handleEditSave: (id: string) => Promise<void>;
  handleEditCancel: () => void;
  setEditTitle: (title: string) => void;
}

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  currentConversationId,
  handleSelectConversation,
  handleEditStart,
  handleDeleteConversation,
  editingId,
  editTitle,
  handleEditSave,
  handleEditCancel,
  setEditTitle,
}) => {
  const [hasArtifacts, setHasArtifacts] = useState(false)

  // Check if conversation has artifacts
  useEffect(() => {
    const checkArtifacts = async () => {
      try {
        if (window.electronAPI?.db) {
          const artifacts = await window.electronAPI.db.getConversationArtifacts(conversation.id)
          setHasArtifacts(artifacts.length > 0)
        }
      } catch (error) {
        console.error('Failed to check artifacts:', error)
      }
    }
    checkArtifacts()
  }, [conversation.id])

  // Truncate title to 25 characters (more space with wider sidebar)
  const truncatedTitle = conversation.title.length > 25
    ? conversation.title.substring(0, 25) + '...'
    : conversation.title

  return (
    <div
      key={conversation.id}
      className={`
        u1-sidebar-item group relative flex items-center px-2 py-1.5 mx-1 transition-all duration-200 cursor-pointer
        ${currentConversationId === conversation.id
          ? 'bg-primary/10 border-l-2 border-primary text-supplement1'
          : 'hover:bg-gray-700/50 text-gray-300 hover:text-supplement1'
        }
      `}
      onClick={() => handleSelectConversation(conversation.id)}
    >
      {/* Chat/Pin Icon - Show pin if has artifacts, otherwise chat icon */}
      {hasArtifacts ? (
        <PaperclipIcon className={`h-3.5 w-3.5 shrink-0 mr-2 ${currentConversationId === conversation.id ? 'text-primary' : 'text-yellow-400'}`} />
      ) : (
        <MessageSquare className={`h-3.5 w-3.5 shrink-0 mr-2 ${currentConversationId === conversation.id ? 'text-primary' : 'text-gray-400'}`} />
      )}

      {/* Title */}
      <div className="flex-1 min-w-0">
        {editingId === conversation.id ? (
          <input
            type="text"
            value={editTitle}
            onChange={(e) => setEditTitle(e.target.value)}
            onBlur={() => handleEditSave(conversation.id)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') handleEditSave(conversation.id);
              if (e.key === 'Escape') handleEditCancel();
            }}
            className="w-full bg-transparent border-none outline-none text-xs font-medium"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div className="text-xs font-medium truncate" title={conversation.title}>
            {truncatedTitle}
          </div>
        )}
      </div>

      {/* Pinned indicator (separate from artifacts pin) */}
      {conversation.is_pinned === 1 && (
        <Pin className="h-3 w-3 text-primary shrink-0 ml-1" />
      )}

      {/* Action buttons */}
      <div className="opacity-0 group-hover:opacity-100 flex items-center gap-0.5 transition-opacity ml-1">
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleEditStart(conversation.id, conversation.title, e);
          }}
          className="p-0.5 hover:bg-gray-600 rounded text-gray-400 hover:text-supplement1"
          title="Edit conversation"
        >
          <Edit3 className="h-3 w-3" />
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleDeleteConversation(conversation.id, e);
          }}
          className="p-0.5 hover:bg-gray-600 rounded text-gray-400 hover:text-secondary"
          title="Delete conversation"
        >
          <Trash2 className="h-3 w-3" />
        </button>
      </div>
    </div>
  );
};

export default ConversationItem;
