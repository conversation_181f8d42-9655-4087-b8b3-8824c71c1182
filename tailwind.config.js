/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    // Exclude unnecessary files for better performance
    "!./src/**/*.test.{js,ts,jsx,tsx}",
    "!./src/**/*.spec.{js,ts,jsx,tsx}",
    "!./src/**/*.stories.{js,ts,jsx,tsx}",
  ],
  // Enable JIT mode for better performance
  mode: 'jit',
  // Optimize for production
  future: {
    removeDeprecatedGapUtilities: true,
    purgeLayersByDefault: true,
  },
  theme: {
    extend: {
      colors: {
        // ChatLo Brand Colors - Optimized
        primary: "#8AB0BB",      // Teal - Main brand color
        secondary: "#FF8383",    // Coral - Accent/heart color
        tertiary: "#1B3E68",     // Navy - Deep accent
        supplement1: "#D5D8E0",  // Light gray - Text/borders
        supplement2: "#89AFBA",  // Muted teal - Secondary elements
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in-up': 'fadeInUp 0.3s ease-out',
        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeInUp: {
          '0%': {
            opacity: '0',
            transform: 'translateY(10px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        }
      },
      backdropBlur: {
        xs: '2px',
      }
    },
  },
  plugins: [],
  // Optimize CSS output
  corePlugins: {
    // Disable unused core plugins for smaller CSS
    preflight: true,
    container: false, // Not used in the project
    accessibility: true,
    alignContent: true,
    alignItems: true,
    alignSelf: true,
    animation: true,
    appearance: true,
    backdropBlur: true,
    backdropBrightness: false, // Not used
    backdropContrast: false, // Not used
    backdropFilter: true,
    backdropGrayscale: false, // Not used
    backdropHueRotate: false, // Not used
    backdropInvert: false, // Not used
    backdropOpacity: true,
    backdropSaturate: false, // Not used
    backdropSepia: false, // Not used
    backgroundAttachment: false, // Not used
    backgroundClip: true,
    backgroundColor: true,
    backgroundImage: true,
    backgroundOpacity: true,
    backgroundPosition: true,
    backgroundRepeat: true,
    backgroundSize: true,
    blur: true,
    borderCollapse: true,
    borderColor: true,
    borderOpacity: true,
    borderRadius: true,
    borderStyle: true,
    borderWidth: true,
    boxShadow: true,
    boxSizing: true,
    brightness: false, // Not used
    clear: false, // Not used
    contrast: false, // Not used
    cursor: true,
    display: true,
    divideColor: false, // Not used
    divideOpacity: false, // Not used
    divideStyle: false, // Not used
    divideWidth: false, // Not used
    dropShadow: false, // Not used
    fill: true,
    filter: true,
    flex: true,
    flexDirection: true,
    flexGrow: true,
    flexShrink: true,
    flexWrap: true,
    float: false, // Not used
    fontFamily: true,
    fontSize: true,
    fontSmoothing: true,
    fontStyle: true,
    fontVariantNumeric: false, // Not used
    fontWeight: true,
    gap: true,
    gradientColorStops: true,
    grayscale: false, // Not used
    gridAutoColumns: false, // Not used
    gridAutoFlow: false, // Not used
    gridAutoRows: false, // Not used
    gridColumn: false, // Not used
    gridColumnEnd: false, // Not used
    gridColumnStart: false, // Not used
    gridRow: false, // Not used
    gridRowEnd: false, // Not used
    gridRowStart: false, // Not used
    gridTemplateColumns: false, // Not used
    gridTemplateRows: false, // Not used
    height: true,
    hueRotate: false, // Not used
    inset: true,
    invert: false, // Not used
    isolation: false, // Not used
    justifyContent: true,
    justifyItems: false, // Not used
    justifySelf: false, // Not used
    letterSpacing: true,
    lineHeight: true,
    listStylePosition: false, // Not used
    listStyleType: false, // Not used
    margin: true,
    maxHeight: true,
    maxWidth: true,
    minHeight: true,
    minWidth: true,
    mixBlendMode: false, // Not used
    objectFit: true,
    objectPosition: true,
    opacity: true,
    order: false, // Not used
    outline: true,
    overflow: true,
    overscrollBehavior: false, // Not used
    padding: true,
    placeContent: false, // Not used
    placeItems: false, // Not used
    placeSelf: false, // Not used
    placeholderColor: true,
    placeholderOpacity: true,
    pointerEvents: true,
    position: true,
    resize: false, // Not used
    ringColor: true,
    ringOffsetColor: true,
    ringOffsetWidth: true,
    ringOpacity: true,
    ringWidth: true,
    rotate: false, // Not used
    saturate: false, // Not used
    scale: false, // Not used
    sepia: false, // Not used
    skew: false, // Not used
    space: false, // Not used
    stroke: true,
    strokeWidth: true,
    tableLayout: false, // Not used
    textAlign: true,
    textColor: true,
    textDecoration: true,
    textDecorationColor: false, // Not used
    textDecorationStyle: false, // Not used
    textDecorationThickness: false, // Not used
    textIndent: false, // Not used
    textOpacity: true,
    textOverflow: true,
    textTransform: true,
    textUnderlineOffset: false, // Not used
    transform: true,
    transformOrigin: false, // Not used
    transitionDelay: true,
    transitionDuration: true,
    transitionProperty: true,
    transitionTimingFunction: true,
    translate: false, // Not used
    userSelect: true,
    verticalAlign: false, // Not used
    visibility: true,
    whitespace: true,
    width: true,
    wordBreak: false, // Not used
    zIndex: true,
  }
}
