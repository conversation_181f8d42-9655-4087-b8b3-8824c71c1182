#!/usr/bin/env node

/**
 * Build Optimization Script for ChatLo
 * 
 * This script performs additional optimizations after the Vite build:
 * - Analyzes bundle sizes
 * - Optimizes assets
 * - Generates build reports
 * - Validates build integrity
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class BuildOptimizer {
  constructor() {
    this.distDir = path.join(process.cwd(), 'dist')
    this.assetsDir = path.join(this.distDir, 'assets')
    this.buildStats = {
      totalSize: 0,
      gzipSize: 0,
      chunks: [],
      assets: [],
      optimizations: []
    }
  }

  async optimize() {
    console.log('🚀 Starting ChatLo Build Optimization...\n')

    try {
      // Step 1: Run the main build
      await this.runMainBuild()

      // Step 2: Analyze bundle
      await this.analyzeBundles()

      // Step 3: Optimize assets
      await this.optimizeAssets()

      // Step 4: Generate reports
      await this.generateReports()

      // Step 5: Validate build
      await this.validateBuild()

      console.log('\n✅ Build optimization completed successfully!')
      this.printSummary()

    } catch (error) {
      console.error('\n❌ Build optimization failed:', error.message)
      process.exit(1)
    }
  }

  async runMainBuild() {
    console.log('📦 Running main build...')
    try {
      execSync('npm run build', { stdio: 'inherit' })
      this.buildStats.optimizations.push('Main build completed')
    } catch (error) {
      throw new Error(`Main build failed: ${error.message}`)
    }
  }

  async analyzeBundles() {
    console.log('📊 Analyzing bundle sizes...')
    
    if (!fs.existsSync(this.assetsDir)) {
      throw new Error('Assets directory not found')
    }

    const files = fs.readdirSync(this.assetsDir, { recursive: true })
    
    for (const file of files) {
      const filePath = path.join(this.assetsDir, file)
      const stats = fs.statSync(filePath)
      
      if (stats.isFile()) {
        const size = stats.size
        const ext = path.extname(file)
        
        this.buildStats.totalSize += size
        
        if (ext === '.js') {
          this.buildStats.chunks.push({
            name: file,
            size: size,
            sizeKB: Math.round(size / 1024 * 100) / 100,
            type: 'javascript'
          })
        } else if (ext === '.css') {
          this.buildStats.assets.push({
            name: file,
            size: size,
            sizeKB: Math.round(size / 1024 * 100) / 100,
            type: 'stylesheet'
          })
        } else {
          this.buildStats.assets.push({
            name: file,
            size: size,
            sizeKB: Math.round(size / 1024 * 100) / 100,
            type: 'asset'
          })
        }
      }
    }

    // Sort chunks by size
    this.buildStats.chunks.sort((a, b) => b.size - a.size)
    this.buildStats.assets.sort((a, b) => b.size - a.size)

    console.log(`   Total files: ${files.length}`)
    console.log(`   Total size: ${Math.round(this.buildStats.totalSize / 1024)} KB`)
  }

  async optimizeAssets() {
    console.log('🎯 Optimizing assets...')

    // Check for large chunks that might need attention
    const largeChunks = this.buildStats.chunks.filter(chunk => chunk.sizeKB > 500)
    if (largeChunks.length > 0) {
      console.log(`   ⚠️  Found ${largeChunks.length} large chunks (>500KB):`)
      largeChunks.forEach(chunk => {
        console.log(`      - ${chunk.name}: ${chunk.sizeKB} KB`)
      })
      this.buildStats.optimizations.push(`Identified ${largeChunks.length} large chunks for monitoring`)
    }

    // Check for duplicate dependencies
    const jsChunks = this.buildStats.chunks.filter(chunk => chunk.type === 'javascript')
    if (jsChunks.length > 10) {
      console.log(`   ℹ️  Many JS chunks (${jsChunks.length}) - consider consolidating`)
      this.buildStats.optimizations.push('Consider chunk consolidation for better caching')
    }

    // Validate critical files exist
    const criticalFiles = ['index.html']
    for (const file of criticalFiles) {
      const filePath = path.join(this.distDir, file)
      if (!fs.existsSync(filePath)) {
        throw new Error(`Critical file missing: ${file}`)
      }
    }

    this.buildStats.optimizations.push('Asset validation completed')
  }

  async generateReports() {
    console.log('📋 Generating build reports...')

    const report = {
      timestamp: new Date().toISOString(),
      buildStats: this.buildStats,
      recommendations: this.generateRecommendations(),
      performance: {
        totalSizeKB: Math.round(this.buildStats.totalSize / 1024),
        jsChunks: this.buildStats.chunks.length,
        cssFiles: this.buildStats.assets.filter(a => a.type === 'stylesheet').length,
        otherAssets: this.buildStats.assets.filter(a => a.type === 'asset').length
      }
    }

    // Write detailed report
    const reportPath = path.join(this.distDir, 'build-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    // Write summary report
    const summaryPath = path.join(this.distDir, 'build-summary.txt')
    const summary = this.generateSummaryText(report)
    fs.writeFileSync(summaryPath, summary)

    console.log(`   Reports saved to: ${reportPath}`)
    this.buildStats.optimizations.push('Build reports generated')
  }

  generateRecommendations() {
    const recommendations = []

    // Size-based recommendations
    const totalSizeKB = Math.round(this.buildStats.totalSize / 1024)
    if (totalSizeKB > 1000) {
      recommendations.push('Consider implementing more aggressive code splitting')
    }

    // Chunk-based recommendations
    const largeChunks = this.buildStats.chunks.filter(chunk => chunk.sizeKB > 300)
    if (largeChunks.length > 0) {
      recommendations.push(`Review large chunks: ${largeChunks.map(c => c.name).join(', ')}`)
    }

    // Performance recommendations
    if (this.buildStats.chunks.length > 15) {
      recommendations.push('Consider consolidating smaller chunks for better HTTP/2 performance')
    }

    return recommendations
  }

  generateSummaryText(report) {
    return `
ChatLo Build Summary
===================
Build Time: ${report.timestamp}
Total Size: ${report.performance.totalSizeKB} KB
JS Chunks: ${report.performance.jsChunks}
CSS Files: ${report.performance.cssFiles}
Other Assets: ${report.performance.otherAssets}

Top 5 Largest Chunks:
${this.buildStats.chunks.slice(0, 5).map((chunk, i) => 
  `${i + 1}. ${chunk.name} - ${chunk.sizeKB} KB`
).join('\n')}

Optimizations Applied:
${this.buildStats.optimizations.map(opt => `- ${opt}`).join('\n')}

Recommendations:
${report.recommendations.map(rec => `- ${rec}`).join('\n')}
`.trim()
  }

  async validateBuild() {
    console.log('✅ Validating build integrity...')

    // Check if index.html exists and has content
    const indexPath = path.join(this.distDir, 'index.html')
    const indexContent = fs.readFileSync(indexPath, 'utf8')
    
    if (!indexContent.includes('<div id="root">')) {
      throw new Error('index.html missing root div')
    }

    // Check if main JS chunk exists
    const jsChunks = this.buildStats.chunks.filter(chunk => chunk.name.includes('index-'))
    if (jsChunks.length === 0) {
      throw new Error('Main JS chunk not found')
    }

    // Check if CSS exists
    const cssFiles = this.buildStats.assets.filter(asset => asset.type === 'stylesheet')
    if (cssFiles.length === 0) {
      console.log('   ⚠️  No CSS files found - this might be intentional')
    }

    this.buildStats.optimizations.push('Build integrity validated')
  }

  printSummary() {
    console.log('\n📊 Build Summary:')
    console.log(`   Total Size: ${Math.round(this.buildStats.totalSize / 1024)} KB`)
    console.log(`   JS Chunks: ${this.buildStats.chunks.length}`)
    console.log(`   CSS Files: ${this.buildStats.assets.filter(a => a.type === 'stylesheet').length}`)
    console.log(`   Other Assets: ${this.buildStats.assets.filter(a => a.type === 'asset').length}`)
    
    console.log('\n🎯 Optimizations Applied:')
    this.buildStats.optimizations.forEach(opt => {
      console.log(`   ✓ ${opt}`)
    })
  }
}

// Run the optimizer
if (require.main === module) {
  const optimizer = new BuildOptimizer()
  optimizer.optimize().catch(console.error)
}

module.exports = BuildOptimizer
