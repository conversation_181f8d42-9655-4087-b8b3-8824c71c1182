# CORS Setup for Local Model Servers

This document explains how to resolve CORS (Cross-Origin Resource Sharing) issues when connecting to local AI model servers like LM Studio and Ollama during development.

## Problem

When running the ChatLo app in development mode (browser), you may encounter CORS errors like:

```
Access to fetch at 'http://localhost:1234/v1/models' from origin 'http://localhost:5173' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## Solution

The app now includes automatic CORS handling through Vite proxy configuration.

### How It Works

1. **Development Mode**: Uses proxy URLs (`/api/ollama`, `/api/lmstudio`) that are automatically routed through Vite's proxy
2. **Production Mode**: Uses direct URLs (`http://localhost:11434`, `http://localhost:1234`) since Electron doesn't have CORS restrictions

### Configuration

The proxy is configured in `vite.config.ts`:

```typescript
server: {
  proxy: {
    '/api/lmstudio': {
      target: 'http://localhost:1234',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api\/lmstudio/, '')
    },
    '/api/ollama': {
      target: 'http://localhost:11434',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api\/ollama/, '')
    }
  }
}
```

### Environment Variables

You can override the default URLs using environment variables:

```bash
# .env file
VITE_OLLAMA_BASE_URL=http://localhost:11434
VITE_LMSTUDIO_BASE_URL=http://localhost:1234
```

## Troubleshooting

### LM Studio Not Starting
1. Make sure LM Studio is running
2. Check that the local server is enabled in LM Studio settings
3. Verify the port (default: 1234)

### Ollama Not Starting
1. Make sure Ollama is installed and running
2. Check that Ollama is listening on the correct port (default: 11434)
3. Try running `ollama list` in terminal to verify it's working

### Still Getting CORS Errors
1. Restart the development server (`npm run dev`)
2. Clear browser cache
3. Check browser console for detailed error messages
4. Verify the proxy configuration in `vite.config.ts`

## Testing Connection

The app will automatically test connections to both Ollama and LM Studio on startup. Check the browser console for connection status messages.
