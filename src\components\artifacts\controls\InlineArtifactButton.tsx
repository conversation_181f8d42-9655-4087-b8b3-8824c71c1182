import React from 'react'
import { useAppStore } from '../../../store'
import { Artifact } from '../../../types'

interface InlineArtifactButtonProps {
  artifact: Artifact
  variant?: 'default' | 'compact' | 'minimal'
  className?: string
}

export function InlineArtifactButton({ 
  artifact, 
  variant = 'default',
  className = '' 
}: InlineArtifactButtonProps) {
  const { 
    artifacts: { isOpen, currentArtifact },
    openArtifact,
    closeArtifacts
  } = useAppStore()

  const isCurrentArtifact = currentArtifact?.id === artifact.id
  const isArtifactsOpen = isOpen

  const handleClick = () => {
    if (isCurrentArtifact && isArtifactsOpen) {
      // If this artifact is currently open, close the sidebar
      closeArtifacts()
    } else {
      // Open this artifact
      openArtifact(artifact)
    }
  }

  const getButtonContent = () => {
    const icon = getArtifactIcon(artifact.type)
    const label = getArtifactLabel(artifact)
    
    switch (variant) {
      case 'compact':
        return (
          <span className="flex items-center space-x-1">
            <span>{icon}</span>
            <span className="text-xs">{label}</span>
          </span>
        )
      
      case 'minimal':
        return <span>{icon}</span>
      
      default:
        return (
          <span className="flex items-center space-x-2">
            <span>{icon}</span>
            <span>{label}</span>
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </span>
        )
    }
  }

  const getButtonClasses = () => {
    const baseClasses = `
      inline-flex items-center transition-all duration-200 cursor-pointer
      border border-transparent rounded-lg font-medium backdrop-blur-sm
      hover:scale-105 active:scale-95 transform
    `

    const stateClasses = isCurrentArtifact && isArtifactsOpen
      ? 'bg-indigo-600/90 text-white border-indigo-500/50 shadow-lg shadow-indigo-500/25'
      : 'bg-neutral-800/80 text-neutral-300 border-neutral-600/50 hover:bg-neutral-700/90 hover:text-white hover:border-neutral-500/70 hover:shadow-md'

    const sizeClasses = variant === 'compact'
      ? 'px-2 py-1 text-xs'
      : variant === 'minimal'
      ? 'p-1 text-sm'
      : 'px-3 py-1.5 text-sm'

    return `${baseClasses} ${stateClasses} ${sizeClasses} ${className}`
  }

  return (
    <button
      onClick={handleClick}
      className={getButtonClasses()}
      title={`${isCurrentArtifact && isArtifactsOpen ? 'Close' : 'Open'} ${artifact.title}`}
    >
      {getButtonContent()}
    </button>
  )
}

// Multi-artifact button for when multiple artifacts are detected
interface MultiArtifactButtonProps {
  artifacts: Artifact[]
  className?: string
}

export function MultiArtifactButton({ artifacts, className = '' }: MultiArtifactButtonProps) {
  const { 
    artifacts: { isOpen },
    openArtifact,
    closeArtifacts
  } = useAppStore()

  const handleClick = () => {
    if (isOpen) {
      closeArtifacts()
    } else {
      // Open the first artifact
      openArtifact(artifacts[0])
    }
  }

  const buttonClasses = `
    inline-flex items-center space-x-2 px-3 py-1.5 text-sm
    bg-neutral-800 text-neutral-300 border border-neutral-600 rounded-md
    hover:bg-neutral-700 hover:text-white hover:border-neutral-500
    transition-all duration-200 cursor-pointer font-medium
    ${className}
  `

  return (
    <button
      onClick={handleClick}
      className={buttonClasses}
      title={`${isOpen ? 'Close' : 'View'} ${artifacts.length} artifacts`}
    >
      <span>🔔</span>
      <span>View {artifacts.length} Artifacts</span>
      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
      </svg>
    </button>
  )
}

// Floating artifact indicator (appears in bottom-right of message)
interface FloatingArtifactIndicatorProps {
  artifacts: Artifact[]
  className?: string
}

export function FloatingArtifactIndicator({ artifacts, className = '' }: FloatingArtifactIndicatorProps) {
  const { 
    artifacts: { isOpen },
    openArtifact
  } = useAppStore()

  if (artifacts.length === 0) return null

  const handleClick = () => {
    if (!isOpen) {
      openArtifact(artifacts[0])
    }
  }

  const indicatorClasses = `
    absolute bottom-2 right-2 flex items-center space-x-1
    bg-indigo-600/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full
    hover:bg-indigo-700/90 hover:scale-110 transition-all duration-200 cursor-pointer
    shadow-lg shadow-indigo-500/25 border border-indigo-500/50
    animate-pulse hover:animate-none
    ${className}
  `

  return (
    <div
      onClick={handleClick}
      className={indicatorClasses}
      title={`${artifacts.length} artifact${artifacts.length > 1 ? 's' : ''} available`}
    >
      <span>🔔</span>
      <span>{artifacts.length}</span>
    </div>
  )
}

// Helper functions
function getArtifactIcon(type: Artifact['type']): string {
  switch (type) {
    case 'code':
    case 'json':
      return '💻'
    case 'image':
      return '🖼️'
    case 'markdown':
      return '📝'
    case 'mermaid':
      return '📊'
    case 'html':
      return '🌐'
    default:
      return '📄'
  }
}

function getArtifactLabel(artifact: Artifact): string {
  switch (artifact.type) {
    case 'code':
      return `View Code${artifact.metadata.language ? ` (${artifact.metadata.language})` : ''}`
    case 'json':
      return 'View JSON'
    case 'image':
      return 'View Image'
    case 'markdown':
      return 'View Document'
    case 'mermaid':
      return 'View Diagram'
    case 'html':
      return 'View HTML'
    default:
      return 'View Artifact'
  }
}
