import { contextB<PERSON>, ip<PERSON><PERSON><PERSON><PERSON> } from 'electron'

export interface ElectronAPI {
  // Database operations
  db: {
    getConversations: () => Promise<any[]>
    getConversation: (id: string) => Promise<any>
    createConversation: (title: string) => Promise<string>
    updateConversation: (id: string, title: string) => Promise<void>
    deleteConversation: (id: string) => Promise<void>
    addMessage: (conversationId: string, message: any) => Promise<string>
    getMessages: (conversationId: string) => Promise<any[]>
    togglePinMessage: (messageId: string) => Promise<void>
    searchConversations: (searchTerm: string) => Promise<any[]>
    getConversationsWithArtifacts: () => Promise<any[]>
    getArtifacts: (messageId: string) => Promise<any[]>
    addArtifact: (messageId: string, artifact: any) => Promise<string>
    updateArtifact: (id: string, updates: any) => Promise<void>
    removeArtifact: (id: string) => Promise<void>
    getConversationArtifacts: (conversationId: string) => Promise<any[]>
  }

  // Settings
  settings: {
    get: (key: string) => Promise<any>
    set: (key: string, value: any) => Promise<void>
  }

  // File system operations
  files: {
    getChatloFolderPath: () => Promise<string>
    setChatloFolderPath: (path: string) => Promise<void>
    getIndexedFiles: () => Promise<any[]>
    searchFiles: (query: string, limit?: number) => Promise<any[]>
    processFileContent: (fileId: string) => Promise<boolean>
    indexFile: (filePath: string, processContent?: boolean) => Promise<string | null>
    indexAllFiles: () => Promise<void>
    copyFileToUploads: (sourcePath: string, filename?: string) => Promise<string>
    saveContentAsFile: (content: string, filename: string, subfolder?: string) => Promise<string>
    deleteFile: (fileId: string) => Promise<boolean>
    getFileContent: (filePath: string) => Promise<Buffer | null>
    fileExists: (filePath: string) => Promise<boolean>
    showOpenDialog: (options: any) => Promise<any>
    showSaveDialog: (options: any) => Promise<any>
    // File attachment operations
    addFileAttachment: (messageId: string, fileId: string, attachmentType: 'attachment' | 'reference') => Promise<string>
    getFileAttachments: (messageId: string) => Promise<any[]>
    getMessageFiles: (messageId: string) => Promise<any[]>
    removeFileAttachment: (attachmentId: string) => Promise<void>
  }

  // Auto-updater
  updater: {
    checkForUpdates: () => Promise<{ available: boolean; message?: string; error?: string }>
    downloadAndInstall: () => Promise<{ success: boolean; message?: string; error?: string }>
    onCheckingForUpdate: (callback: () => void) => void
    onUpdateAvailable: (callback: (info: any) => void) => void
    onUpdateNotAvailable: (callback: () => void) => void
    onError: (callback: (message: string) => void) => void
    onDownloadProgress: (callback: (progress: any) => void) => void
    onUpdateDownloaded: (callback: (info: any) => void) => void
  }
}

const electronAPI: ElectronAPI = {
  db: {
    getConversations: () => ipcRenderer.invoke('db:getConversations'),
    getConversation: (id: string) => ipcRenderer.invoke('db:getConversation', id),
    createConversation: (title: string) => ipcRenderer.invoke('db:createConversation', title),
    updateConversation: (id: string, title: string) => ipcRenderer.invoke('db:updateConversation', id, title),
    deleteConversation: (id: string) => ipcRenderer.invoke('db:deleteConversation', id),
    addMessage: (conversationId: string, message: any) => ipcRenderer.invoke('db:addMessage', conversationId, message),
    getMessages: (conversationId: string) => ipcRenderer.invoke('db:getMessages', conversationId),
    togglePinMessage: (messageId: string) => ipcRenderer.invoke('db:togglePinMessage', messageId),
    searchConversations: (searchTerm: string) => ipcRenderer.invoke('db:searchConversations', searchTerm),
    getConversationsWithArtifacts: () => ipcRenderer.invoke('db:getConversationsWithArtifacts'),
    getArtifacts: (messageId: string) => ipcRenderer.invoke('db:getArtifacts', messageId),
    addArtifact: (messageId: string, artifact: any) => ipcRenderer.invoke('db:addArtifact', messageId, artifact),
    updateArtifact: (id: string, updates: any) => ipcRenderer.invoke('db:updateArtifact', id, updates),
    removeArtifact: (id: string) => ipcRenderer.invoke('db:removeArtifact', id),
    getConversationArtifacts: (conversationId: string) => ipcRenderer.invoke('db:getConversationArtifacts', conversationId),
  },
  settings: {
    get: (key: string) => ipcRenderer.invoke('settings:get', key),
    set: (key: string, value: any) => ipcRenderer.invoke('settings:set', key, value),
  },
  files: {
    getChatloFolderPath: () => ipcRenderer.invoke('files:getChatloFolderPath'),
    setChatloFolderPath: (path: string) => ipcRenderer.invoke('files:setChatloFolderPath', path),
    getIndexedFiles: () => ipcRenderer.invoke('files:getIndexedFiles'),
    searchFiles: (query: string, limit?: number) => ipcRenderer.invoke('files:searchFiles', query, limit),
    processFileContent: (fileId: string) => ipcRenderer.invoke('files:processFileContent', fileId),
    indexFile: (filePath: string, processContent?: boolean) => ipcRenderer.invoke('files:indexFile', filePath, processContent),
    indexAllFiles: () => ipcRenderer.invoke('files:indexAllFiles'),
    copyFileToUploads: (sourcePath: string, filename?: string) => ipcRenderer.invoke('files:copyFileToUploads', sourcePath, filename),
    saveContentAsFile: (content: string, filename: string, subfolder?: string) => ipcRenderer.invoke('files:saveContentAsFile', content, filename, subfolder),
    deleteFile: (fileId: string) => ipcRenderer.invoke('files:deleteFile', fileId),
    getFileContent: (filePath: string) => ipcRenderer.invoke('files:getFileContent', filePath),
    fileExists: (filePath: string) => ipcRenderer.invoke('files:fileExists', filePath),
    showOpenDialog: (options: any) => ipcRenderer.invoke('files:showOpenDialog', options),
    showSaveDialog: (options: any) => ipcRenderer.invoke('files:showSaveDialog', options),
    // File attachment operations
    addFileAttachment: (messageId: string, fileId: string, attachmentType: 'attachment' | 'reference') => ipcRenderer.invoke('files:addFileAttachment', messageId, fileId, attachmentType),
    getFileAttachments: (messageId: string) => ipcRenderer.invoke('files:getFileAttachments', messageId),
    getMessageFiles: (messageId: string) => ipcRenderer.invoke('files:getMessageFiles', messageId),
    removeFileAttachment: (attachmentId: string) => ipcRenderer.invoke('files:removeFileAttachment', attachmentId)
  },
  updater: {
    checkForUpdates: () => ipcRenderer.invoke('updater:check-for-updates'),
    downloadAndInstall: () => ipcRenderer.invoke('updater:download-and-install'),
    onCheckingForUpdate: (callback: () => void) => ipcRenderer.on('updater:checking-for-update', () => callback()),
    onUpdateAvailable: (callback: (info: any) => void) => ipcRenderer.on('updater:update-available', (_, info) => callback(info)),
    onUpdateNotAvailable: (callback: () => void) => ipcRenderer.on('updater:update-not-available', () => callback()),
    onError: (callback: (message: string) => void) => ipcRenderer.on('updater:error', (_, message) => callback(message)),
    onDownloadProgress: (callback: (progress: any) => void) => ipcRenderer.on('updater:download-progress', (_, progress) => callback(progress)),
    onUpdateDownloaded: (callback: (info: any) => void) => ipcRenderer.on('updater:update-downloaded', (_, info) => callback(info)),
  },
}

contextBridge.exposeInMainWorld('electronAPI', electronAPI)

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
