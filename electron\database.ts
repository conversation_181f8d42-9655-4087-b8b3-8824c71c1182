import Database from 'better-sqlite3'
import path from 'path'
import { app } from 'electron'
import { v4 as uuidv4 } from 'uuid'
import { isDev } from './utils'

export interface Conversation {
  id: string
  title: string
  is_pinned: 0 | 1
  created_at: string
  updated_at: string
}

export interface Message {
  id: string
  conversation_id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  model?: string
  is_pinned?: 0 | 1
  created_at: string
}

export interface FileRecord {
  id: string
  filename: string
  filepath: string
  file_type: string
  file_size: number
  content_hash: string
  mime_type?: string
  extracted_content?: string
  metadata?: string // JSON
  created_at: string
  updated_at: string
}

export interface FileAttachment {
  id: string
  message_id: string
  file_id: string
  attachment_type: 'attachment' | 'reference'
  created_at: string
}

export interface Artifact {
  id: string
  message_id: string
  type: 'image' | 'code' | 'markdown' | 'mermaid' | 'html' | 'json'
  title: string
  content: string
  metadata: string // JSON
  original_index: number
  created_at: string
}

export class DatabaseManager {
  private db: Database.Database
  private currentVersion = 4

  constructor() {
    const dbPath = isDev
      ? path.join(__dirname, '../chatlo-dev.db')
      : path.join(app.getPath('userData'), 'chatlo.db')

    console.log('Initializing database at:', dbPath)

    this.db = new Database(dbPath)
    this.init()
  }

  private init(): void {
    // Enable WAL mode for better performance
    this.db.pragma('journal_mode = WAL')

    // Create tables
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS conversations (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        is_pinned INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        conversation_id TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
        content TEXT NOT NULL,
        model TEXT,
        is_pinned INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS files (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        filepath TEXT NOT NULL,
        file_type TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        content_hash TEXT NOT NULL,
        mime_type TEXT,
        extracted_content TEXT,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS file_attachments (
        id TEXT PRIMARY KEY,
        message_id TEXT NOT NULL,
        file_id TEXT NOT NULL,
        attachment_type TEXT NOT NULL CHECK (attachment_type IN ('attachment', 'reference')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE,
        FOREIGN KEY (file_id) REFERENCES files (id) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS artifacts (
        id TEXT PRIMARY KEY,
        message_id TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('image', 'code', 'markdown', 'mermaid', 'html', 'json')),
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        metadata TEXT,
        original_index INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE
      );

      CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages (conversation_id);
      CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages (created_at);
      CREATE INDEX IF NOT EXISTS idx_artifacts_message_id ON artifacts (message_id);
    `)

    // Run migrations
    this.runMigrations()

    console.log('Database initialized successfully')
  }

  private runMigrations(): void {
    // Get current database version
    let version = 0
    try {
      const result = this.db.prepare('SELECT value FROM settings WHERE key = ?').get('db_version') as { value: string } | undefined
      version = result ? parseInt(JSON.parse(result.value)) : 0
    } catch (error) {
      // Settings table might not exist yet
      version = 0
    }

    // Run migrations if needed
    if (version < this.currentVersion) {
      console.log(`Running database migrations from version ${version} to ${this.currentVersion}`)

      // Migration v2 -> v3: Add mime_type column to files table
      if (version < 3) {
        try {
          const tableInfo = this.db.prepare("PRAGMA table_info(files)").all() as any[]
          const hasMimeType = tableInfo.some(column => column.name === 'mime_type')

          if (!hasMimeType) {
            console.log('Migration v3: Adding mime_type column to files table')
            this.db.exec('ALTER TABLE files ADD COLUMN mime_type TEXT')

            // Update existing image files with proper MIME types
            const imageFiles = this.db.prepare("SELECT id, filename FROM files WHERE file_type = 'image'").all() as any[]
            const updateStmt = this.db.prepare('UPDATE files SET mime_type = ? WHERE id = ?')

            for (const file of imageFiles) {
              const ext = file.filename.toLowerCase().split('.').pop()
              let mimeType = 'image/png' // default

              switch (ext) {
                case 'jpg':
                case 'jpeg':
                  mimeType = 'image/jpeg'
                  break
                case 'png':
                  mimeType = 'image/png'
                  break
                case 'gif':
                  mimeType = 'image/gif'
                  break
                case 'webp':
                  mimeType = 'image/webp'
                  break
                case 'bmp':
                  mimeType = 'image/bmp'
                  break
                case 'svg':
                  mimeType = 'image/svg+xml'
                  break
              }

              updateStmt.run(mimeType, file.id)
            }

            console.log(`Updated ${imageFiles.length} existing image files with MIME types`)
          }
        } catch (error) {
          console.error('Error in migration v3:', error)
        }
      }

      // Migration v3 -> v4: Add artifacts table
      if (version < 4) {
        try {
          console.log('Migration v4: Creating artifacts table')
          this.db.exec(`
            CREATE TABLE IF NOT EXISTS artifacts (
              id TEXT PRIMARY KEY,
              message_id TEXT NOT NULL,
              type TEXT NOT NULL CHECK (type IN ('image', 'code', 'markdown', 'mermaid', 'html', 'json')),
              title TEXT NOT NULL,
              content TEXT NOT NULL,
              metadata TEXT,
              original_index INTEGER NOT NULL,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE
            );

            CREATE INDEX IF NOT EXISTS idx_artifacts_message_id ON artifacts (message_id);
          `)
          console.log('Artifacts table created successfully')
        } catch (error) {
          console.error('Error in migration v4:', error)
        }
      }

      this.setSetting('db_version', this.currentVersion)
      console.log('Database migrations completed')
    }
  }

  close(): void {
    if (this.db) {
      this.db.close()
    }
  }

  // Conversations
  getConversations(): Conversation[] {
    const stmt = this.db.prepare('SELECT * FROM conversations ORDER BY updated_at DESC')
    return stmt.all() as Conversation[]
  }

  getConversation(id: string): Conversation | null {
    const stmt = this.db.prepare('SELECT * FROM conversations WHERE id = ?')
    return stmt.get(id) as Conversation | null
  }

  createConversation(title: string): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare('INSERT INTO conversations (id, title, is_pinned, created_at, updated_at) VALUES (?, ?, ?, ?, ?)')
    stmt.run(id, title, 0, now, now)
    return id
  }

  updateConversation(id: string, title: string): void {
    const now = new Date().toISOString()
    const stmt = this.db.prepare('UPDATE conversations SET title = ?, updated_at = ? WHERE id = ?')
    stmt.run(title, now, id)
  }

  togglePinConversation(id: string): void {
    const now = new Date().toISOString()
    const stmt = this.db.prepare('UPDATE conversations SET is_pinned = NOT is_pinned, updated_at = ? WHERE id = ?')
    stmt.run(now, id)
  }

  togglePinMessage(id: string): void {
    const stmt = this.db.prepare('UPDATE messages SET is_pinned = NOT is_pinned WHERE id = ?')
    stmt.run(id)
  }

  deleteConversation(id: string): void {
    const stmt = this.db.prepare('DELETE FROM conversations WHERE id = ?')
    stmt.run(id)
  }

  searchConversationsAndMessages(searchTerm: string): Conversation[] {
    const stmt = this.db.prepare(`
      SELECT c.*
      FROM conversations c
      WHERE c.id IN (
        SELECT id FROM conversations WHERE title LIKE ? ESCAPE '\\'
        UNION
        SELECT conversation_id FROM messages WHERE content LIKE ? ESCAPE '\\'
      )
      ORDER BY c.updated_at DESC
    `)
    const term = `%${searchTerm.replace(/%/g, '\\%').replace(/_/g, '\\_')}%`
    return stmt.all(term, term) as Conversation[]
  }

  // Messages
  getMessages(conversationId: string): Message[] {
    const stmt = this.db.prepare('SELECT * FROM messages WHERE conversation_id = ? ORDER BY created_at ASC')
    return stmt.all(conversationId) as Message[]
  }

  addMessage(conversationId: string, message: Omit<Message, 'id' | 'conversation_id' | 'created_at'>): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare('INSERT INTO messages (id, conversation_id, role, content, model, is_pinned, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)')
    stmt.run(id, conversationId, message.role, message.content, message.model || null, message.is_pinned || 0, now)

    // Update conversation timestamp
    const updateStmt = this.db.prepare('UPDATE conversations SET updated_at = ? WHERE id = ?')
    updateStmt.run(now, conversationId)

    return id
  }

  // Settings
  getSetting(key: string): any {
    const stmt = this.db.prepare('SELECT value FROM settings WHERE key = ?')
    const result = stmt.get(key) as { value: string } | undefined
    return result ? JSON.parse(result.value) : null
  }

  setSetting(key: string, value: any): void {
    const stmt = this.db.prepare('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)')
    stmt.run(key, JSON.stringify(value))
  }

  // Files
  getFiles(): FileRecord[] {
    const stmt = this.db.prepare('SELECT * FROM files ORDER BY updated_at DESC')
    return stmt.all() as FileRecord[]
  }

  getFile(id: string): FileRecord | null {
    const stmt = this.db.prepare('SELECT * FROM files WHERE id = ?')
    return stmt.get(id) as FileRecord | null
  }

  getFileByPath(filepath: string): FileRecord | null {
    const stmt = this.db.prepare('SELECT * FROM files WHERE filepath = ?')
    return stmt.get(filepath) as FileRecord | null
  }

  addFile(file: Omit<FileRecord, 'id' | 'created_at' | 'updated_at'>): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare(`
      INSERT INTO files (id, filename, filepath, file_type, file_size, content_hash, mime_type, extracted_content, metadata, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)
    stmt.run(id, file.filename, file.filepath, file.file_type, file.file_size, file.content_hash, file.mime_type || null, file.extracted_content || null, file.metadata || null, now, now)
    return id
  }

  updateFile(id: string, updates: Partial<Omit<FileRecord, 'id' | 'created_at'>>): void {
    const now = new Date().toISOString()
    const fields = Object.keys(updates).filter(key => key !== 'id' && key !== 'created_at')
    if (fields.length === 0) return

    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => updates[field as keyof typeof updates])
    values.push(now, id)

    const stmt = this.db.prepare(`UPDATE files SET ${setClause}, updated_at = ? WHERE id = ?`)
    stmt.run(...values)
  }

  deleteFile(id: string): void {
    const stmt = this.db.prepare('DELETE FROM files WHERE id = ?')
    stmt.run(id)
  }

  searchFiles(searchTerm: string, limit: number = 10): FileRecord[] {
    if (!searchTerm.trim()) {
      // Return recent files when no search term
      const stmt = this.db.prepare('SELECT * FROM files ORDER BY updated_at DESC LIMIT ?')
      return stmt.all(limit) as FileRecord[]
    }

    // Prioritize filename matches over content matches for better performance
    const stmt = this.db.prepare(`
      SELECT *,
        CASE
          WHEN filename LIKE ? ESCAPE '\\' THEN 1
          WHEN extracted_content LIKE ? ESCAPE '\\' THEN 2
          ELSE 3
        END as match_priority
      FROM files
      WHERE filename LIKE ? ESCAPE '\\'
         OR extracted_content LIKE ? ESCAPE '\\'
      ORDER BY match_priority, updated_at DESC
      LIMIT ?
    `)
    const term = `%${searchTerm.replace(/%/g, '\\%').replace(/_/g, '\\_')}%`
    return stmt.all(term, term, term, term, limit) as FileRecord[]
  }

  // File Attachments
  getFileAttachments(messageId: string): FileAttachment[] {
    const stmt = this.db.prepare('SELECT * FROM file_attachments WHERE message_id = ?')
    return stmt.all(messageId) as FileAttachment[]
  }

  addFileAttachment(messageId: string, fileId: string, attachmentType: 'attachment' | 'reference'): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare('INSERT INTO file_attachments (id, message_id, file_id, attachment_type, created_at) VALUES (?, ?, ?, ?, ?)')
    stmt.run(id, messageId, fileId, attachmentType, now)
    return id
  }

  removeFileAttachment(id: string): void {
    const stmt = this.db.prepare('DELETE FROM file_attachments WHERE id = ?')
    stmt.run(id)
  }

  getMessageFiles(messageId: string): (FileRecord & { attachment_id: string; attachment_type: string })[] {
    const stmt = this.db.prepare(`
      SELECT f.*, fa.id as attachment_id, fa.attachment_type, fa.created_at as attachment_created_at
      FROM files f
      JOIN file_attachments fa ON f.id = fa.file_id
      WHERE fa.message_id = ?
    `)
    return stmt.all(messageId) as (FileRecord & { attachment_id: string; attachment_type: string })[]
  }

  // Artifacts
  getArtifacts(messageId: string): Artifact[] {
    const stmt = this.db.prepare('SELECT * FROM artifacts WHERE message_id = ? ORDER BY original_index')
    return stmt.all(messageId) as Artifact[]
  }

  addArtifact(messageId: string, artifact: Omit<Artifact, 'id' | 'message_id' | 'created_at'>): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare(`
      INSERT INTO artifacts (id, message_id, type, title, content, metadata, original_index, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `)
    stmt.run(id, messageId, artifact.type, artifact.title, artifact.content, artifact.metadata, artifact.original_index, now)
    return id
  }

  updateArtifact(id: string, updates: Partial<Pick<Artifact, 'title' | 'content' | 'metadata'>>): void {
    const fields = []
    const values = []

    if (updates.title !== undefined) {
      fields.push('title = ?')
      values.push(updates.title)
    }
    if (updates.content !== undefined) {
      fields.push('content = ?')
      values.push(updates.content)
    }
    if (updates.metadata !== undefined) {
      fields.push('metadata = ?')
      values.push(updates.metadata)
    }

    if (fields.length > 0) {
      values.push(id)
      const stmt = this.db.prepare(`UPDATE artifacts SET ${fields.join(', ')} WHERE id = ?`)
      stmt.run(...values)
    }
  }

  removeArtifact(id: string): void {
    const stmt = this.db.prepare('DELETE FROM artifacts WHERE id = ?')
    stmt.run(id)
  }

  getConversationArtifacts(conversationId: string): (Artifact & { message_created_at: string })[] {
    const stmt = this.db.prepare(`
      SELECT a.*, m.created_at as message_created_at
      FROM artifacts a
      JOIN messages m ON a.message_id = m.id
      WHERE m.conversation_id = ?
      ORDER BY m.created_at DESC, a.original_index
    `)
    return stmt.all(conversationId) as (Artifact & { message_created_at: string })[]
  }
}
