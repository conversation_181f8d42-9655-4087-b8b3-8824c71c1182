// Lazy loading utilities for heavy modules and components

interface ModuleCache {
  [key: string]: any
}

class LazyModuleLoader {
  private static cache: ModuleCache = {}
  private static loadingPromises: { [key: string]: Promise<any> } = {}

  // Load heavy file processing modules only when needed
  static async loadFileProcessor(type: 'pdf' | 'office' | 'image' | 'ocr'): Promise<any> {
    const cacheKey = `fileProcessor-${type}`
    
    // Return cached module if available
    if (this.cache[cacheKey]) {
      return this.cache[cacheKey]
    }

    // Return existing loading promise if already loading
    if (this.loadingPromises[cacheKey]) {
      return this.loadingPromises[cacheKey]
    }

    // Start loading the module
    this.loadingPromises[cacheKey] = this.dynamicImportProcessor(type)
    
    try {
      const module = await this.loadingPromises[cacheKey]
      this.cache[cacheKey] = module
      delete this.loadingPromises[cacheKey]
      return module
    } catch (error) {
      delete this.loadingPromises[cacheKey]
      throw error
    }
  }

  private static async dynamicImportProcessor(type: string): Promise<any> {
    switch (type) {
      case 'pdf':
        // PDF processing is handled by Electron backend
        return { available: true, backend: true }
      
      case 'office':
        // Office document processing is handled by Electron backend
        return { available: true, backend: true }
      
      case 'image':
        // Use Canvas API for basic image processing
        return this.loadCanvasImageProcessor()
      
      case 'ocr':
        // OCR is handled by Electron backend
        return { available: true, backend: true }
      
      default:
        throw new Error(`Unknown processor type: ${type}`)
    }
  }

  private static async loadCanvasImageProcessor() {
    // Canvas-based image processing for basic operations
    return {
      available: true,
      resizeImage: (file: File, maxWidth: number, maxHeight: number): Promise<Blob> => {
        return new Promise((resolve, reject) => {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          const img = new Image()
          
          img.onload = () => {
            // Calculate new dimensions
            let { width, height } = img
            
            if (width > maxWidth) {
              height = (height * maxWidth) / width
              width = maxWidth
            }
            
            if (height > maxHeight) {
              width = (width * maxHeight) / height
              height = maxHeight
            }
            
            canvas.width = width
            canvas.height = height
            
            // Draw and convert to blob
            ctx?.drawImage(img, 0, 0, width, height)
            canvas.toBlob((blob) => {
              if (blob) {
                resolve(blob)
              } else {
                reject(new Error('Failed to create blob'))
              }
            }, 'image/jpeg', 0.8)
          }
          
          img.onerror = () => reject(new Error('Failed to load image'))
          img.src = URL.createObjectURL(file)
        })
      },
      
      compressImage: (file: File, quality: number = 0.8): Promise<Blob> => {
        return new Promise((resolve, reject) => {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          const img = new Image()
          
          img.onload = () => {
            canvas.width = img.width
            canvas.height = img.height
            ctx?.drawImage(img, 0, 0)
            
            canvas.toBlob((blob) => {
              if (blob) {
                resolve(blob)
              } else {
                reject(new Error('Failed to compress image'))
              }
            }, 'image/jpeg', quality)
          }
          
          img.onerror = () => reject(new Error('Failed to load image'))
          img.src = URL.createObjectURL(file)
        })
      }
    }
  }

  // Load React components lazily
  static async loadComponent(componentName: string): Promise<any> {
    const cacheKey = `component-${componentName}`
    
    if (this.cache[cacheKey]) {
      return this.cache[cacheKey]
    }

    if (this.loadingPromises[cacheKey]) {
      return this.loadingPromises[cacheKey]
    }

    this.loadingPromises[cacheKey] = this.dynamicImportComponent(componentName)
    
    try {
      const component = await this.loadingPromises[cacheKey]
      this.cache[cacheKey] = component
      delete this.loadingPromises[cacheKey]
      return component
    } catch (error) {
      delete this.loadingPromises[cacheKey]
      throw error
    }
  }

  private static async dynamicImportComponent(componentName: string): Promise<any> {
    switch (componentName) {
      case 'ImageEditor':
        return import('../components/ImageEditor')
      
      case 'FilePicker':
        return import('../components/FilePicker')
      
      case 'ModelSelector':
        return import('../components/ModelSelector')
      
      case 'ChatSettingsDrawer':
        return import('../components/ChatSettingsDrawer')
      
      default:
        throw new Error(`Unknown component: ${componentName}`)
    }
  }

  // Check if a module is available without loading it
  static isModuleAvailable(type: string): boolean {
    return this.cache[`fileProcessor-${type}`] || this.cache[`component-${type}`] ? true : false
  }

  // Clear cache (useful for testing or memory management)
  static clearCache(): void {
    this.cache = {}
    this.loadingPromises = {}
  }

  // Get cache size for debugging
  static getCacheInfo(): { size: number; modules: string[] } {
    const modules = Object.keys(this.cache)
    return {
      size: modules.length,
      modules
    }
  }
}

// Export singleton instance
export const lazyLoader = LazyModuleLoader

// Convenience functions
export const loadFileProcessor = (type: 'pdf' | 'office' | 'image' | 'ocr') => 
  lazyLoader.loadFileProcessor(type)

export const loadComponent = (componentName: string) => 
  lazyLoader.loadComponent(componentName)

export const isModuleAvailable = (type: string) => 
  lazyLoader.isModuleAvailable(type)

// Feature detection utility
export const detectAvailableFeatures = async (): Promise<{
  pdf: boolean
  office: boolean
  image: boolean
  ocr: boolean
}> => {
  const features = {
    pdf: false,
    office: false,
    image: false,
    ocr: false
  }

  try {
    // Check if Electron API is available for backend processing
    if (window.electronAPI?.files) {
      features.pdf = true
      features.office = true
      features.ocr = true
    }

    // Canvas API is always available in browsers
    features.image = true
  } catch (error) {
    console.warn('Feature detection failed:', error)
  }

  return features
}

// Memory usage optimization
export const optimizeMemoryUsage = (): void => {
  // Clear unused modules from cache after 5 minutes of inactivity
  setTimeout(() => {
    const cacheInfo = lazyLoader.getCacheInfo()
    if (cacheInfo.size > 5) {
      console.log('Clearing lazy loader cache to optimize memory')
      lazyLoader.clearCache()
    }
  }, 5 * 60 * 1000) // 5 minutes
}

// Initialize memory optimization
if (typeof window !== 'undefined') {
  optimizeMemoryUsage()
}
