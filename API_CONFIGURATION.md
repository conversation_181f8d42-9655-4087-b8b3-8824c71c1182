# API & Server Configuration

ChatLo supports a flexible BYOK (Bring Your Own Key) model with configurable API endpoints and local model servers. All configuration is done through the Settings UI.

## Overview

ChatLo can connect to:
- **ChatLo Cloud Services** (with ChatLo API key)
- **OpenRouter** (with OpenRouter API key)
- **Local Model Servers** (Ollama, LM Studio)

## Settings UI

Access the settings through the gear icon in the main interface. The settings are organized into two tabs:

### API Keys Tab
Configure your API keys for cloud services:

#### ChatLo API Key
- **Purpose**: Access to ChatLo cloud services and premium features
- **Format**: `chatlo-...`
- **Required**: Optional (for premium features)

#### OpenRouter API Key
- **Purpose**: Access to 100+ AI models through OpenRouter
- **Format**: `sk-or-...`
- **Required**: Yes (for cloud AI models)
- **Get Key**: [OpenRouter Keys](https://openrouter.ai/keys)

### Local Servers Tab
Configure local AI model servers:

#### Ollama Configuration
- **Default Host**: `localhost`
- **Default Port**: `11434`
- **Enable/Disable**: Toggle to enable/disable Ollama integration
- **Custom Host**: Configure for remote Ollama instances

#### LM Studio Configuration
- **Default Host**: `localhost`
- **Default Port**: `1234`
- **Enable/Disable**: Toggle to enable/disable LM Studio integration
- **Custom Host**: Configure for remote LM Studio instances

## CORS Handling

### Development Mode
- Uses direct URLs to local servers (no proxy)
- LM Studio: `http://127.0.0.1:1234` (CORS enabled by default)
- Ollama: `http://localhost:11434` (CORS enabled by default)

### Production Mode (Electron)
- Uses direct URLs configured in settings
- No CORS restrictions in Electron environment

### CORS Configuration
Both LM Studio and Ollama have CORS enabled by default for local development. If you encounter CORS issues:

**LM Studio**:
- Go to Settings → Server
- Ensure "Enable CORS" is checked
- Restart the server

**Ollama**:
- CORS is enabled by default
- If issues persist, restart Ollama service

## Configuration Examples

### Local Development
```
Ollama: localhost:11434 (enabled)
LM Studio: localhost:1234 (enabled)
```

### Remote Servers
```
Ollama: *************:11434 (enabled)
LM Studio: *************:1234 (enabled)
```

### Hybrid Setup
```
ChatLo API Key: chatlo-xxx-xxx
OpenRouter API Key: sk-or-xxx-xxx
Ollama: localhost:11434 (enabled)
LM Studio: disabled
```

## Testing Connections

Use the "Test Connections" button in the Local Servers tab to verify:
- Server availability
- Model discovery
- Connection status

Results show:
- ✅ Connected servers with model count
- ❌ Failed connections with error details

## Troubleshooting

### Common Issues

1. **CORS Errors in Development**
   - Restart development server: `npm run dev`
   - Check proxy configuration in `vite.config.ts`

2. **Local Server Not Found**
   - Verify server is running
   - Check host/port configuration
   - Test with `curl http://localhost:11434/api/tags` (Ollama)
   - Test with `curl http://localhost:1234/v1/models` (LM Studio)

3. **API Key Issues**
   - Verify key format and validity
   - Check network connectivity
   - Use "Test API Key" button

### Server Setup

#### Ollama
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama
ollama serve

# Pull a model
ollama pull llama2
```

#### LM Studio
1. Download from [LM Studio](https://lmstudio.ai/)
2. Load a model
3. Start local server (default port 1234)
4. Enable CORS in server settings

## Security Notes

- API keys are stored securely in Electron's secure storage
- Local server connections use HTTP (consider HTTPS for production)
- Settings are encrypted when saved to disk
- No API keys are logged or transmitted except to their respective services

## BYOK Model Benefits

- **Cost Control**: Use your own API credits
- **Privacy**: Direct connection to services
- **Flexibility**: Choose your preferred models and providers
- **Hybrid**: Mix cloud and local models as needed
- **Scalability**: Configure multiple local servers
