import React, { useState } from 'react'
import { FileRecord } from '../types'
import { X, File, Image, FileText, FileSpreadsheet, Presentation, Zap, AlertCircle } from './Icons'
import ImagePreview from './ImagePreview'

interface FileAttachmentsProps {
  attachedFiles: FileRecord[]
  onRemoveFile: (fileId: string) => void
  onProcessFile?: (fileId: string) => Promise<void>
}

const FileAttachments: React.FC<FileAttachmentsProps> = ({ attachedFiles, onRemoveFile, onProcessFile }) => {
  const [processingFiles, setProcessingFiles] = useState<Set<string>>(new Set())

  if (attachedFiles.length === 0) return null

  const handleProcessFile = async (fileId: string) => {
    if (!onProcessFile) return

    setProcessingFiles(prev => new Set(prev).add(fileId))
    try {
      await onProcessFile(fileId)
    } finally {
      setProcessingFiles(prev => {
        const newSet = new Set(prev)
        newSet.delete(fileId)
        return newSet
      })
    }
  }

  const getFileIcon = (fileType: string, mimeType?: string) => {
    switch (fileType) {
      case 'image':
        return <Image className="w-4 h-4 text-blue-400" />
      case 'pdf':
        return <FileText className="w-4 h-4 text-red-400" />
      case 'word':
        return <FileText className="w-4 h-4 text-blue-600" />
      case 'excel':
        return <FileSpreadsheet className="w-4 h-4 text-green-600" />
      case 'powerpoint':
        return <Presentation className="w-4 h-4 text-orange-600" />
      default:
        return <File className="w-4 h-4 text-gray-400" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  // Separate images from other files
  const imageFiles = attachedFiles.filter(file => file.file_type === 'image')
  const otherFiles = attachedFiles.filter(file => file.file_type !== 'image')

  return (
    <div className="mb-3 space-y-3">
      {/* Images Section */}
      {imageFiles.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Image className="w-4 h-4 text-neutral-400" />
            <span className="text-sm text-neutral-400">
              {imageFiles.length} image{imageFiles.length > 1 ? 's' : ''} attached
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {imageFiles.map((file) => (
              <div key={file.id} className="relative">
                <ImagePreview
                  file={file}
                  onRemove={() => onRemoveFile(file.id)}
                />
                {!file.extracted_content && onProcessFile && (
                  <button
                    onClick={() => handleProcessFile(file.id)}
                    disabled={processingFiles.has(file.id)}
                    className="absolute top-2 left-2 p-2 bg-indigo-500 hover:bg-indigo-600 rounded-full text-white shadow-lg transition-all disabled:opacity-50"
                    title="Process image for AI processing"
                  >
                    {processingFiles.has(file.id) ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Zap className="w-4 h-4" />
                    )}
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Other Files Section */}
      {otherFiles.length > 0 && (
        <div className="p-3 bg-neutral-800/50 rounded-lg border border-neutral-700">
          <div className="flex items-center gap-2 mb-2">
            <File className="w-4 h-4 text-neutral-400" />
            <span className="text-sm text-neutral-400">
              {otherFiles.length} file{otherFiles.length > 1 ? 's' : ''} attached
            </span>
          </div>

          <div className="space-y-2">
            {otherFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center gap-3 p-2 bg-neutral-900/50 rounded border border-neutral-600 group hover:border-neutral-500 transition-colors"
              >
                {getFileIcon(file.file_type, file.mime_type)}

                <div className="flex-1 min-w-0">
                  <div className="text-sm text-white truncate">
                    {file.filename}
                  </div>
                  <div className="text-xs text-neutral-400">
                    {formatFileSize(file.file_size)} • {file.file_type}
                    {file.extracted_content ? (
                      <span className="ml-2 text-green-400">✓ Processed</span>
                    ) : (
                      <span className="ml-2 text-yellow-400 flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        Not processed
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-1">
                  {!file.extracted_content && onProcessFile && (
                    <button
                      onClick={() => handleProcessFile(file.id)}
                      disabled={processingFiles.has(file.id)}
                      className="opacity-0 group-hover:opacity-100 p-1 hover:bg-indigo-600 bg-indigo-500 rounded transition-all text-white disabled:opacity-50"
                      title="Process file for AI processing"
                    >
                      {processingFiles.has(file.id) ? (
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <Zap className="w-4 h-4" />
                      )}
                    </button>
                  )}

                  <button
                    onClick={() => onRemoveFile(file.id)}
                    className="opacity-0 group-hover:opacity-100 p-1 hover:bg-neutral-700 rounded transition-all"
                    title="Remove file"
                  >
                    <X className="w-4 h-4 text-neutral-400 hover:text-red-400" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default FileAttachments
