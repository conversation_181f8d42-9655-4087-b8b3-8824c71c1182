import { useState, lazy, Suspense } from 'react'
import { useAppStore } from './store'
import IconBar from './components/IconBar'
import MobileNavBar from './components/MobileNavBar'
import Sidebar from './components/Sidebar'
import ChatArea from './components/ChatArea'
import { ArtifactsSidebar } from './components/artifacts/ArtifactsSidebar'
import { ToastProvider } from './components/artifacts/controls/ArtifactToast'
import { HashRouter as Router, Routes, Route } from 'react-router-dom'

// Lazy load heavy components
const HistoryPage = lazy(() => import('./pages/HistoryPage'))
const SettingsPage = lazy(() => import('./pages/SettingsPage'))
const PerformanceMonitor = lazy(() => import('./components/PerformanceMonitor'))

function App() {
  const { sidebarOpen, setSidebarOpen } = useAppStore()
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false)

  return (
    <Router>
      <ToastProvider>
        <div className="h-screen flex flex-col md:flex-row bg-gray-900 text-white font-sans antialiased selection:bg-primary/60 overflow-hidden">

        {/* Mobile Navigation Bar */}
        <MobileNavBar />

        {/* Desktop Layout */}
        <div className="flex flex-1 min-h-0">
          {/* VSCode-style Icon Bar */}
          <IconBar />

          {/* Sidebar */}
          <div className={`
            ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
            md:translate-x-0 transition-transform duration-300 ease-in-out
            fixed md:relative z-40 h-full
            ${sidebarOpen ? 'left-0' : 'left-0'}
            md:left-0
          `}>
            <Sidebar />
          </div>

          {/* Overlay for mobile */}
          {sidebarOpen && (
            <div
              className="md:hidden fixed inset-0 bg-black/50 z-30"
              onClick={() => setSidebarOpen(false)}
            />
          )}

          {/* Main content area */}
          <div className="flex-1 flex flex-col min-w-0 h-full">
            <Suspense fallback={
              <div className="flex-1 flex items-center justify-center">
                <div className="text-supplement1">Loading...</div>
              </div>
            }>
              <Routes>
                <Route path="/" element={<ChatArea />} />
                <Route path="/history" element={<HistoryPage />} />
                <Route path="/settings" element={<SettingsPage />} />
              </Routes>
            </Suspense>
          </div>

          {/* Artifacts Sidebar */}
          <ArtifactsSidebar />
        </div>

        {/* Performance Monitor */}
        {showPerformanceMonitor && (
          <Suspense fallback={null}>
            <PerformanceMonitor
              isVisible={showPerformanceMonitor}
              onToggle={() => setShowPerformanceMonitor(!showPerformanceMonitor)}
            />
          </Suspense>
        )}
        </div>
      </ToastProvider>
    </Router>
  )
}

export default App
