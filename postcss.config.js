module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {
      // Optimize for modern browsers
      overrideBrowserslist: [
        'Chrome >= 90',
        'Firefox >= 88',
        'Safari >= 14',
        'Edge >= 90'
      ],
      // Remove unnecessary prefixes
      remove: true,
      // Add prefixes only when needed
      add: true,
      // Optimize grid support
      grid: 'autoplace'
    },
    // CSS optimization plugins
    ...(process.env.NODE_ENV === 'production' && {
      'postcss-combine-duplicated-selectors': {
        removeDuplicatedProperties: true
      },
      'postcss-merge-rules': {},
      'postcss-discard-empty': {},
      'postcss-discard-comments': {
        removeAll: true
      },
      'postcss-normalize-whitespace': {},
      'postcss-minify-selectors': {},
      'postcss-reduce-initial': {},
      'postcss-convert-values': {},
      'postcss-calc': {
        precision: 3
      },
      'postcss-colormin': {},
      'postcss-ordered-values': {},
      'postcss-unique-selectors': {},
      'postcss-merge-longhand': {},
      'postcss-discard-overridden': {},
      'postcss-normalize-positions': {},
      'postcss-normalize-repeat-style': {},
      'postcss-normalize-string': {},
      'postcss-normalize-timing-functions': {},
      'postcss-normalize-unicode': {},
      'postcss-normalize-url': {},
      'postcss-svgo': {
        plugins: [
          {
            name: 'preset-default',
            params: {
              overrides: {
                removeViewBox: false,
              },
            },
          },
        ],
      }
    })
  },
}
